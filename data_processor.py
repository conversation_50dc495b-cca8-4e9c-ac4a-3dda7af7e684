"""
数据处理和导出模块
"""

import pandas as pd
import json
import os
from typing import List, Dict
from datetime import datetime
import logging
from config import OUTPUT_DIR, OUTPUT_FORMATS

logger = logging.getLogger(__name__)

class DataProcessor:
    def __init__(self):
        self.ensure_output_dir()
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
            logger.info(f"创建输出目录: {OUTPUT_DIR}")
            
    def clean_data(self, pairs_data: List[Dict]) -> List[Dict]:
        """清洗和验证数据"""
        cleaned_data = []
        
        for pair in pairs_data:
            # 跳过没有基本信息的币对
            if not pair.get("pair_address") or not pair.get("base_token_symbol"):
                continue
                
            # 确保数值字段为数字类型
            numeric_fields = [
                "price_usd", "liquidity_usd", "liquidity_base", "liquidity_quote",
                "market_cap", "fdv", "volume_24h", "volume_6h", "volume_1h", "volume_5m"
            ]
            
            for field in numeric_fields:
                if field in pair and pair[field] is not None:
                    try:
                        pair[field] = float(pair[field])
                    except (ValueError, TypeError):
                        pair[field] = None
                        
            cleaned_data.append(pair)
            
        logger.info(f"数据清洗完成，保留 {len(cleaned_data)} 个有效币对")
        return cleaned_data
        
    def calculate_statistics(self, pairs_data: List[Dict]) -> Dict:
        """计算统计信息"""
        if not pairs_data:
            return {}
            
        df = pd.DataFrame(pairs_data)
        
        stats = {
            "total_pairs": len(pairs_data),
            "unique_dexes": df["dex_id"].nunique() if "dex_id" in df.columns else 0,
            "unique_base_tokens": df["base_token_symbol"].nunique() if "base_token_symbol" in df.columns else 0,
            "unique_quote_tokens": df["quote_token_symbol"].nunique() if "quote_token_symbol" in df.columns else 0,
        }
        
        # 流动性统计
        if "liquidity_usd" in df.columns:
            liquidity_data = df["liquidity_usd"].dropna()
            if not liquidity_data.empty:
                stats.update({
                    "total_liquidity_usd": liquidity_data.sum(),
                    "avg_liquidity_usd": liquidity_data.mean(),
                    "median_liquidity_usd": liquidity_data.median(),
                    "max_liquidity_usd": liquidity_data.max(),
                    "min_liquidity_usd": liquidity_data.min(),
                })
                
        # 成交量统计
        if "volume_24h" in df.columns:
            volume_data = df["volume_24h"].dropna()
            if not volume_data.empty:
                stats.update({
                    "total_volume_24h": volume_data.sum(),
                    "avg_volume_24h": volume_data.mean(),
                    "median_volume_24h": volume_data.median(),
                    "max_volume_24h": volume_data.max(),
                    "min_volume_24h": volume_data.min(),
                })
                
        # DEX 分布
        if "dex_id" in df.columns:
            dex_counts = df["dex_id"].value_counts().to_dict()
            stats["dex_distribution"] = dex_counts
            
        return stats
        
    def save_to_json(self, data: List[Dict], filename: str = None) -> str:
        """保存数据为JSON格式"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"arbitrum_pairs_{timestamp}.json"
            
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        logger.info(f"数据已保存到 JSON 文件: {filepath}")
        return filepath
        
    def save_to_csv(self, data: List[Dict], filename: str = None) -> str:
        """保存数据为CSV格式"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"arbitrum_pairs_{timestamp}.csv"
            
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        df = pd.DataFrame(data)
        df.to_csv(filepath, index=False, encoding='utf-8')
        
        logger.info(f"数据已保存到 CSV 文件: {filepath}")
        return filepath
        
    def save_statistics(self, stats: Dict, filename: str = None) -> str:
        """保存统计信息"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"arbitrum_stats_{timestamp}.json"
            
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
            
        logger.info(f"统计信息已保存到: {filepath}")
        return filepath
        
    def create_summary_report(self, pairs_data: List[Dict], stats: Dict) -> str:
        """创建汇总报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"arbitrum_report_{timestamp}.txt"
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("=== Arbitrum DEX 币对数据报告 ===\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("=== 基本统计 ===\n")
            f.write(f"总币对数量: {stats.get('total_pairs', 0)}\n")
            f.write(f"唯一DEX数量: {stats.get('unique_dexes', 0)}\n")
            f.write(f"唯一基础代币数量: {stats.get('unique_base_tokens', 0)}\n")
            f.write(f"唯一报价代币数量: {stats.get('unique_quote_tokens', 0)}\n\n")
            
            if "total_liquidity_usd" in stats:
                f.write("=== 流动性统计 ===\n")
                f.write(f"总流动性: ${stats['total_liquidity_usd']:,.2f}\n")
                f.write(f"平均流动性: ${stats['avg_liquidity_usd']:,.2f}\n")
                f.write(f"中位数流动性: ${stats['median_liquidity_usd']:,.2f}\n")
                f.write(f"最大流动性: ${stats['max_liquidity_usd']:,.2f}\n")
                f.write(f"最小流动性: ${stats['min_liquidity_usd']:,.2f}\n\n")
                
            if "total_volume_24h" in stats:
                f.write("=== 24小时成交量统计 ===\n")
                f.write(f"总成交量: ${stats['total_volume_24h']:,.2f}\n")
                f.write(f"平均成交量: ${stats['avg_volume_24h']:,.2f}\n")
                f.write(f"中位数成交量: ${stats['median_volume_24h']:,.2f}\n")
                f.write(f"最大成交量: ${stats['max_volume_24h']:,.2f}\n")
                f.write(f"最小成交量: ${stats['min_volume_24h']:,.2f}\n\n")
                
            if "dex_distribution" in stats:
                f.write("=== DEX 分布 ===\n")
                for dex, count in sorted(stats["dex_distribution"].items(), 
                                       key=lambda x: x[1], reverse=True):
                    f.write(f"{dex}: {count} 个币对\n")
                    
        logger.info(f"汇总报告已保存到: {filepath}")
        return filepath
        
    def process_and_save(self, raw_pairs_data: List[Dict]) -> Dict[str, str]:
        """处理数据并保存到多种格式"""
        logger.info("开始处理和保存数据...")
        
        # 清洗数据
        cleaned_data = self.clean_data(raw_pairs_data)
        
        # 计算统计信息
        stats = self.calculate_statistics(cleaned_data)
        
        # 保存文件
        saved_files = {}
        
        # 保存JSON格式
        if "json" in OUTPUT_FORMATS:
            saved_files["json"] = self.save_to_json(cleaned_data)
            
        # 保存CSV格式
        if "csv" in OUTPUT_FORMATS:
            saved_files["csv"] = self.save_to_csv(cleaned_data)
            
        # 保存统计信息
        saved_files["stats"] = self.save_statistics(stats)
        
        # 创建汇总报告
        saved_files["report"] = self.create_summary_report(cleaned_data, stats)
        
        logger.info("数据处理和保存完成")
        return saved_files
