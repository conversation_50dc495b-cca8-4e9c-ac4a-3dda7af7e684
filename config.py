"""
DexScreener API 配置文件
"""

# API 配置
BASE_URL = "https://api.dexscreener.com"
ENDPOINTS = {
    "search": "/latest/dex/search",
    "pairs": "/latest/dex/pairs/{chainId}/{pairId}",
    "token_pairs": "/token-pairs/v1/{chainId}/{tokenAddress}",
    "tokens": "/tokens/v1/{chainId}/{tokenAddresses}"
}

# 请求限制配置
RATE_LIMITS = {
    "search": 300,  # 每分钟300次请求
    "pairs": 300,   # 每分钟300次请求
    "token_pairs": 300,  # 每分钟300次请求
    "tokens": 300   # 每分钟300次请求
}

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 1  # 秒
BACKOFF_FACTOR = 2

# 支持的链ID
SUPPORTED_CHAINS = [
    "ethereum", "bsc", "polygon", "avalanche", "fantom", 
    "cronos", "arbitrum", "optimism", "base", "solana"
]

# Arbitrum 相关配置
ARBITRUM_CHAIN_ID = "arbitrum"

# 输出配置
OUTPUT_DIR = "output"
OUTPUT_FORMATS = ["json", "csv"]

# 数据字段配置
REQUIRED_FIELDS = [
    "chainId", "dexId", "pairAddress", "baseToken", "quoteToken",
    "priceUsd", "volume", "liquidity", "marketCap", "fdv"
]

# 搜索关键词（用于获取更多币对）
SEARCH_KEYWORDS = [
    # 主要代币
    "ETH", "USDC", "USDT", "WETH", "ARB", "GMX", "MAGIC",
    "DPX", "JONES", "RDNT", "GRAIL", "PENDLE",

    # 更多 Arbitrum 生态代币
    "GNS", "VELA", "UMAMI", "PLUTUS", "DOPEX", "LYRA",
    "PREMIA", "HEGIC", "SPERAX", "TREASURE", "SMOL",
    "LODE", "BATTLEFLY", "REALM", "SEED", "ELK",

    # DeFi 协议代币
    "AAVE", "UNI", "SUSHI", "CRV", "BAL", "COMP",
    "MKR", "SNX", "YFI", "1INCH", "ALPHA", "CREAM",

    # 稳定币和包装代币
    "DAI", "FRAX", "LUSD", "MIM", "USDC.e", "WBTC",
    "renBTC", "LINK", "MATIC", "AVAX", "FTM",

    # 其他热门代币
    "SHIB", "DOGE", "PEPE", "FLOKI", "WOJAK", "MEME",
    "APE", "BLUR", "LDO", "RPL", "FXS", "CVX",

    # 通用搜索词
    "USD", "BTC", "TOKEN", "COIN", "SWAP", "POOL"
]

# 额外的搜索策略
ADDITIONAL_SEARCH_STRATEGIES = [
    # 按字母搜索
    "A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
    # 数字搜索
    "1", "2", "3", "4", "5",
    # 常见前缀
    "W", "X", "Y", "Z"
]
