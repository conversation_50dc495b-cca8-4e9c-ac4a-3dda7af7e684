#!/usr/bin/env python3
"""
DexScreener Arbitrum 币对数据获取主程序
"""

import click
import logging
import sys
from typing import List, Dict
from dex_screener_fetcher import DexScreenerFetcher
from data_processor import DataProcessor
from config import ARBITRUM_CHAIN_ID

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('dex_screener.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@click.command()
@click.option('--verbose', '-v', is_flag=True, help='启用详细日志输出')
@click.option('--output-dir', '-o', default='output', help='输出目录路径')
@click.option('--max-pairs', '-m', type=int, help='最大获取币对数量（用于测试）')
def main(verbose: bool, output_dir: str, max_pairs: int):
    """
    获取 Arbitrum 链上的所有 DEX 币对数据，包括成交量和池子大小
    """
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        
    logger.info("=== DexScreener Arbitrum 数据获取器启动 ===")
    logger.info(f"目标链: {ARBITRUM_CHAIN_ID}")
    logger.info(f"输出目录: {output_dir}")
    
    try:
        # 初始化获取器和处理器
        fetcher = DexScreenerFetcher()
        processor = DataProcessor()
        
        # 获取币对数据
        logger.info("开始获取 Arbitrum 币对数据...")
        pairs_data = fetcher.get_arbitrum_pairs()
        
        if not pairs_data:
            logger.error("未获取到任何币对数据")
            sys.exit(1)
            
        # 限制数量（用于测试）
        if max_pairs and len(pairs_data) > max_pairs:
            logger.info(f"限制输出到前 {max_pairs} 个币对")
            pairs_data = pairs_data[:max_pairs]
            
        # 提取详细信息
        logger.info("提取币对详细信息...")
        processed_pairs = []
        
        for pair in pairs_data:
            pair_info = fetcher.extract_pair_info(pair)
            if pair_info:
                processed_pairs.append(pair_info)
                
        logger.info(f"成功处理 {len(processed_pairs)} 个币对")
        
        # 处理和保存数据
        saved_files = processor.process_and_save(processed_pairs)
        
        # 输出结果摘要
        logger.info("=== 数据获取完成 ===")
        logger.info(f"总共获取币对: {len(processed_pairs)}")
        logger.info("保存的文件:")
        for file_type, filepath in saved_files.items():
            logger.info(f"  {file_type.upper()}: {filepath}")
            
        # 显示前几个币对的信息作为示例
        if processed_pairs:
            logger.info("\n=== 示例币对信息 ===")
            for i, pair in enumerate(processed_pairs[:3]):
                logger.info(f"\n币对 {i+1}:")
                logger.info(f"  DEX: {pair.get('dex_id')}")
                logger.info(f"  交易对: {pair.get('base_token_symbol')}/{pair.get('quote_token_symbol')}")
                logger.info(f"  价格: ${pair.get('price_usd')}")
                logger.info(f"  24h成交量: ${pair.get('volume_24h'):,.2f}" if pair.get('volume_24h') else "  24h成交量: N/A")
                logger.info(f"  流动性: ${pair.get('liquidity_usd'):,.2f}" if pair.get('liquidity_usd') else "  流动性: N/A")
                
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行出错: {e}", exc_info=True)
        sys.exit(1)

def show_pair_summary(pairs: List[Dict]):
    """显示币对数据摘要"""
    if not pairs:
        return
        
    # 统计信息
    total_pairs = len(pairs)
    dexes = set(pair.get('dex_id') for pair in pairs if pair.get('dex_id'))
    base_tokens = set(pair.get('base_token_symbol') for pair in pairs if pair.get('base_token_symbol'))
    
    # 流动性和成交量统计
    liquidity_values = [pair.get('liquidity_usd') for pair in pairs if pair.get('liquidity_usd')]
    volume_values = [pair.get('volume_24h') for pair in pairs if pair.get('volume_24h')]
    
    print(f"\n=== 数据摘要 ===")
    print(f"总币对数: {total_pairs}")
    print(f"涉及DEX数: {len(dexes)}")
    print(f"基础代币数: {len(base_tokens)}")
    
    if liquidity_values:
        total_liquidity = sum(liquidity_values)
        avg_liquidity = total_liquidity / len(liquidity_values)
        print(f"总流动性: ${total_liquidity:,.2f}")
        print(f"平均流动性: ${avg_liquidity:,.2f}")
        
    if volume_values:
        total_volume = sum(volume_values)
        avg_volume = total_volume / len(volume_values)
        print(f"总24h成交量: ${total_volume:,.2f}")
        print(f"平均24h成交量: ${avg_volume:,.2f}")
        
    print(f"\n主要DEX: {', '.join(list(dexes)[:10])}")
    print(f"主要代币: {', '.join(list(base_tokens)[:10])}")

if __name__ == "__main__":
    main()
