"""
DexScreener API 数据获取器
"""

import requests
import time
import json
from typing import List, Dict, Optional, Set
from tqdm import tqdm
import logging
from config import *

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DexScreenerFetcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.last_request_time = 0
        self.request_count = 0
        self.collected_pairs = set()  # 用于去重
        self.api_calls_log = []  # 记录所有API调用
        
    def _rate_limit(self, endpoint_type: str = "search"):
        """实现请求频率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # 每分钟最多300次请求，即每0.2秒一次
        min_interval = 60 / RATE_LIMITS[endpoint_type]
        
        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            time.sleep(sleep_time)
            
        self.last_request_time = time.time()
        self.request_count += 1
        
    def _make_request(self, url: str, params: Dict = None, endpoint_type: str = "search") -> Optional[Dict]:
        """发送HTTP请求，包含重试机制"""
        self._rate_limit(endpoint_type)

        # 记录API调用
        api_call_info = {
            "url": url,
            "params": params,
            "endpoint_type": endpoint_type,
            "timestamp": time.time(),
            "request_number": self.request_count + 1
        }
        self.api_calls_log.append(api_call_info)

        # 输出API调用信息
        params_str = f"?{requests.compat.urlencode(params)}" if params else ""
        full_url = f"{url}{params_str}"
        logger.info(f"API调用 #{self.request_count + 1}: {full_url}")

        for attempt in range(MAX_RETRIES):
            try:
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                result = response.json()

                # 记录响应信息
                api_call_info["status"] = "success"
                api_call_info["response_size"] = len(str(result)) if result else 0

                return result

            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY * (BACKOFF_FACTOR ** attempt))
                else:
                    logger.error(f"请求最终失败: {url}")
                    api_call_info["status"] = "failed"
                    api_call_info["error"] = str(e)
                    return None
                    
    def search_pairs(self, query: str) -> List[Dict]:
        """搜索币对"""
        url = f"{BASE_URL}{ENDPOINTS['search']}"
        params = {"q": query}
        
        logger.info(f"搜索币对: {query}")
        data = self._make_request(url, params)
        
        if data and "pairs" in data:
            pairs = data["pairs"]
            logger.info(f"找到 {len(pairs)} 个币对")
            return pairs
        else:
            logger.warning(f"搜索 '{query}' 未返回数据")
            return []
            
    def get_arbitrum_pairs(self) -> List[Dict]:
        """获取 Arbitrum 链上的所有币对"""
        all_pairs = []

        logger.info("开始获取 Arbitrum 链上的币对数据...")

        # 使用多个搜索关键词来获取更多币对
        for keyword in tqdm(SEARCH_KEYWORDS, desc="搜索关键词"):
            pairs = self.search_pairs(keyword)

            # 过滤出 Arbitrum 链上的币对
            arbitrum_pairs = [
                pair for pair in pairs
                if pair.get("chainId") == ARBITRUM_CHAIN_ID
            ]

            # 去重处理
            for pair in arbitrum_pairs:
                pair_address = pair.get("pairAddress")
                if pair_address and pair_address not in self.collected_pairs:
                    self.collected_pairs.add(pair_address)
                    all_pairs.append(pair)

            logger.info(f"关键词 '{keyword}' 找到 {len(arbitrum_pairs)} 个 Arbitrum 币对")

        logger.info(f"第一轮搜索收集到 {len(all_pairs)} 个唯一的 Arbitrum 币对")

        # 第二轮：使用额外的搜索策略
        logger.info("开始第二轮搜索...")
        for keyword in tqdm(ADDITIONAL_SEARCH_STRATEGIES, desc="额外搜索"):
            pairs = self.search_pairs(keyword)

            # 过滤出 Arbitrum 链上的币对
            arbitrum_pairs = [
                pair for pair in pairs
                if pair.get("chainId") == ARBITRUM_CHAIN_ID
            ]

            # 去重处理
            new_pairs_count = 0
            for pair in arbitrum_pairs:
                pair_address = pair.get("pairAddress")
                if pair_address and pair_address not in self.collected_pairs:
                    self.collected_pairs.add(pair_address)
                    all_pairs.append(pair)
                    new_pairs_count += 1

            if new_pairs_count > 0:
                logger.info(f"额外搜索 '{keyword}' 找到 {new_pairs_count} 个新的 Arbitrum 币对")

        # 第三轮：通过已知代币地址获取更多币对
        logger.info("开始第三轮搜索：通过代币地址获取币对...")
        token_pairs = self.get_pairs_by_token_addresses()

        # 去重处理
        new_token_pairs_count = 0
        for pair in token_pairs:
            pair_address = pair.get("pairAddress")
            if pair_address and pair_address not in self.collected_pairs:
                self.collected_pairs.add(pair_address)
                all_pairs.append(pair)
                new_token_pairs_count += 1

        if new_token_pairs_count > 0:
            logger.info(f"通过代币地址找到 {new_token_pairs_count} 个新的币对")

        logger.info(f"总共收集到 {len(all_pairs)} 个唯一的 Arbitrum 币对")
        return all_pairs

    def get_pairs_by_token_addresses(self) -> List[Dict]:
        """通过已知的 Arbitrum 代币地址获取币对"""
        # Arbitrum 上的主要代币地址
        arbitrum_token_addresses = [
            "******************************************",  # ARB
            "******************************************",  # WETH
            "******************************************",  # USDC (bridged)
            "******************************************",  # USDC (native)
            "******************************************",  # USDT
            "******************************************",  # DAI
            "******************************************",  # WBTC
            "******************************************",  # GMX
            "******************************************",  # MAGIC
            "******************************************",  # DPX
            "******************************************",  # JONES
            "******************************************",  # RDNT
            "******************************************",  # GRAIL
            "******************************************",  # PENDLE
        ]

        all_token_pairs = []

        for token_address in tqdm(arbitrum_token_addresses, desc="代币地址搜索"):
            try:
                url = f"{BASE_URL}{ENDPOINTS['token_pairs'].format(chainId=ARBITRUM_CHAIN_ID, tokenAddress=token_address)}"
                data = self._make_request(url, endpoint_type="token_pairs")

                if data and isinstance(data, list):
                    all_token_pairs.extend(data)
                    logger.info(f"代币 {token_address[:10]}... 找到 {len(data)} 个币对")

            except Exception as e:
                logger.warning(f"获取代币 {token_address} 的币对时出错: {e}")
                continue

        logger.info(f"通过代币地址总共找到 {len(all_token_pairs)} 个币对")
        return all_token_pairs

    def get_api_calls_summary(self) -> Dict:
        """获取API调用摘要"""
        total_calls = len(self.api_calls_log)
        successful_calls = len([call for call in self.api_calls_log if call.get("status") == "success"])
        failed_calls = total_calls - successful_calls

        # 按端点类型统计
        endpoint_stats = {}
        for call in self.api_calls_log:
            endpoint = call.get("endpoint_type", "unknown")
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {"total": 0, "success": 0, "failed": 0}
            endpoint_stats[endpoint]["total"] += 1
            if call.get("status") == "success":
                endpoint_stats[endpoint]["success"] += 1
            else:
                endpoint_stats[endpoint]["failed"] += 1

        return {
            "total_calls": total_calls,
            "successful_calls": successful_calls,
            "failed_calls": failed_calls,
            "endpoint_statistics": endpoint_stats,
            "all_calls": self.api_calls_log
        }

    def save_api_calls_log(self, filename: str = None) -> str:
        """保存API调用日志"""
        import json
        from datetime import datetime

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"api_calls_log_{timestamp}.json"

        filepath = f"output/{filename}"

        summary = self.get_api_calls_summary()

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        logger.info(f"API调用日志已保存到: {filepath}")
        return filepath
        
    def get_pair_details(self, chain_id: str, pair_id: str) -> Optional[Dict]:
        """获取特定币对的详细信息"""
        url = f"{BASE_URL}{ENDPOINTS['pairs'].format(chainId=chain_id, pairId=pair_id)}"
        
        data = self._make_request(url, endpoint_type="pairs")
        
        if data and "pairs" in data and len(data["pairs"]) > 0:
            return data["pairs"][0]
        return None
        
    def extract_pair_info(self, pair: Dict) -> Dict:
        """提取币对的关键信息"""
        try:
            # 基础信息
            info = {
                "chain_id": pair.get("chainId"),
                "dex_id": pair.get("dexId"),
                "pair_address": pair.get("pairAddress"),
                "url": pair.get("url"),
                "labels": pair.get("labels", []),
                
                # 代币信息
                "base_token_address": pair.get("baseToken", {}).get("address"),
                "base_token_name": pair.get("baseToken", {}).get("name"),
                "base_token_symbol": pair.get("baseToken", {}).get("symbol"),
                
                "quote_token_address": pair.get("quoteToken", {}).get("address"),
                "quote_token_name": pair.get("quoteToken", {}).get("name"),
                "quote_token_symbol": pair.get("quoteToken", {}).get("symbol"),
                
                # 价格信息
                "price_native": pair.get("priceNative"),
                "price_usd": pair.get("priceUsd"),
                
                # 流动性信息（池子大小）
                "liquidity_usd": pair.get("liquidity", {}).get("usd"),
                "liquidity_base": pair.get("liquidity", {}).get("base"),
                "liquidity_quote": pair.get("liquidity", {}).get("quote"),
                
                # 市值信息
                "market_cap": pair.get("marketCap"),
                "fdv": pair.get("fdv"),
                
                # 创建时间
                "pair_created_at": pair.get("pairCreatedAt"),
            }
            
            # 成交量信息
            volume = pair.get("volume", {})
            info.update({
                "volume_24h": volume.get("h24"),
                "volume_6h": volume.get("h6"),
                "volume_1h": volume.get("h1"),
                "volume_5m": volume.get("m5"),
            })
            
            # 交易次数
            txns = pair.get("txns", {})
            for period in ["m5", "h1", "h6", "h24"]:
                if period in txns:
                    info[f"buys_{period}"] = txns[period].get("buys")
                    info[f"sells_{period}"] = txns[period].get("sells")
                    
            # 价格变化
            price_change = pair.get("priceChange", {})
            info.update({
                "price_change_5m": price_change.get("m5"),
                "price_change_1h": price_change.get("h1"),
                "price_change_6h": price_change.get("h6"),
                "price_change_24h": price_change.get("h24"),
            })
            
            return info
            
        except Exception as e:
            logger.error(f"提取币对信息时出错: {e}")
            return {}
